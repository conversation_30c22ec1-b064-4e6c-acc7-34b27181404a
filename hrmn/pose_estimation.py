#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gi, signal, sys, cv2, hailo
gi.require_version('Gst', '1.0')
from gi.repository import Gst, GLib

# ----------------------------------------------------------------------
# Hailo
# ----------------------------------------------------------------------
from hailo_apps_infra.hailo_rpi_common import (
    get_caps_from_pad,
    get_numpy_from_buffer,
    app_callback_class,
)
from hailo_apps_infra.pose_estimation_pipeline import GStreamerPoseEstimationApp

# ----------------------------------------------------------------------
# Shared Memory
# ----------------------------------------------------------------------
from shared_memory import init_pose_shm, POSE_SHM_NAME

MAX_PERSONS = 10
NUM_KPTS    = 17

pose_shm, pose_arr = init_pose_shm(MAX_PERSONS, NUM_KPTS)
# shape = (MAX_PERSONS,17,3)

# ----------------------------------------------------------------------
class UserAppCallback(app_callback_class):
    def __init__(self):
        super().__init__()

def app_callback(pad, info, user_data):
    buf = info.get_buffer()
    if not buf:
        return Gst.PadProbeReturn.OK

    # Frame → Array zurücksetzen
    pose_arr.fill(-1.0)

    user_data.increment()
    fmt, W, H = get_caps_from_pad(pad)
    frame = None
    if user_data.use_frame and None not in (fmt, W, H):
        frame = get_numpy_from_buffer(buf, fmt, W, H)

    import hailo
    roi = hailo.get_roi_from_buffer(buf)
    detections = roi.get_objects_typed(hailo.HAILO_DETECTION)

    
        
    person_idx = 0
    for det in detections:
        if det.get_label() != "person":
            continue
        if person_idx >= MAX_PERSONS:
            break

        lms = det.get_objects_typed(hailo.HAILO_LANDMARKS)
        if not lms:
            continue
        points = lms[0].get_points()
        bb = det.get_bbox()  # normalisierte BBox

        for kp_i, p in enumerate(points):
            x_norm = p.x() * bb.width() + bb.xmin()
            y_norm = p.y() * bb.height() + bb.ymin()
            conf   = getattr(p, "score", lambda:1.)()

            pose_arr[person_idx, kp_i] = (x_norm, y_norm, conf)

            # optionales Overlay
            if frame is not None:
                x_px = int(x_norm * W)
                y_px = int(y_norm * H)
                cv2.circle(frame, (x_px, y_px), 4, (0,255,0), -1)

        person_idx += 1

    # → Pipeline zurück
    if frame is not None:
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
        user_data.set_frame(frame)

    return Gst.PadProbeReturn.OK

# ----------------------------------------------------------------------
def cleanup(*_):
    print("\n[INFO] Kill pose_estimation.py – unlink shm …")
    pose_shm.close()
    pose_shm.unlink()
    sys.exit(0)

signal.signal(signal.SIGINT, cleanup)

# ----------------------------------------------------------------------
if __name__ == "__main__":
    print(f"[INFO] schreibe Pose-Daten in SHM='{POSE_SHM_NAME}'")
    user_data = UserAppCallback()
    app = GStreamerPoseEstimationApp(app_callback, user_data)
    try:
        app.run()
    finally:
        cleanup()
