#!/bin/bash

# Source the setup_env.sh file to set the TAPPAS_POST_PROC_DIR environment variable
if [ -f "/home/<USER>/Documents/taha_ai/hailo-rpi5-examples/setup_env.sh" ]; then
    echo "Sourcing setup_env.sh from hailo-rpi5-examples..."
    source /home/<USER>/Documents/taha_ai/hailo-rpi5-examples/setup_env.sh
elif [ -f "/home/<USER>/Documents/taha_ai/venv_hailo/setup_env.sh" ]; then
    echo "Sourcing setup_env.sh from venv_hailo..."
    source /home/<USER>/Documents/taha_ai/venv_hailo/setup_env.sh
else
    echo "Error: Could not find setup_env.sh file."
    exit 1
fi

# Run the hand_pose_estimation.py script with the provided arguments
python hand_pose_estimation.py "$@"
